// DO NOT EDIT. This file is generated by Fresh.
// This file SHOULD be checked into source version control.
// This file is automatically updated during development when running `dev.ts`.

import * as $_404 from "./routes/_404.tsx";
import * as $_app from "./routes/_app.tsx";
import * as $_layout from "./routes/_layout.tsx";
import * as $_middleware from "./routes/_middleware.ts";
import * as $analytics_dashboard from "./routes/analytics-dashboard.tsx";
import * as $analytics from "./routes/analytics.tsx";
import * as $analytics_attribution from "./routes/analytics/attribution.tsx";
import * as $analytics_cohorts from "./routes/analytics/cohorts.tsx";
import * as $analytics_d3_dashboard from "./routes/analytics/d3-dashboard.tsx";
import * as $analytics_enhanced from "./routes/analytics/enhanced.tsx";
import * as $analytics_realtime from "./routes/analytics/realtime.tsx";
import * as $api_analytics_path_ from "./routes/api/analytics/[...path].ts";
import * as $api_analytics_enhanced_attribution_channels from "./routes/api/analytics/enhanced/attribution/channels.ts";
import * as $api_analytics_enhanced_attribution_journeys from "./routes/api/analytics/enhanced/attribution/journeys.ts";
import * as $api_analytics_enhanced_attribution_models from "./routes/api/analytics/enhanced/attribution/models.ts";
import * as $api_analytics_enhanced_cohorts_analysis from "./routes/api/analytics/enhanced/cohorts/analysis.ts";
import * as $api_analytics_enhanced_cohorts_retention_curves from "./routes/api/analytics/enhanced/cohorts/retention-curves.ts";
import * as $api_analytics_enhanced_dashboard from "./routes/api/analytics/enhanced/dashboard.ts";
import * as $api_analytics_enhanced_realtime from "./routes/api/analytics/enhanced/realtime.ts";
import * as $api_analytics_enhanced_realtime_events from "./routes/api/analytics/enhanced/realtime/events.ts";
import * as $api_analytics_enhanced_realtime_funnel from "./routes/api/analytics/enhanced/realtime/funnel.ts";
import * as $api_analytics_enhanced_realtime_geography from "./routes/api/analytics/enhanced/realtime/geography.ts";
import * as $api_analytics_enhanced_realtime_metrics from "./routes/api/analytics/enhanced/realtime/metrics.ts";
import * as $api_auth_login from "./routes/api/auth/login.ts";
import * as $api_auth_logout from "./routes/api/auth/logout.ts";
import * as $api_dashboard_metrics from "./routes/api/dashboard/metrics.ts";
import * as $api_dashboard_overview from "./routes/api/dashboard/overview.ts";
import * as $api_health from "./routes/api/health.ts";
import * as $api_realtime_metrics from "./routes/api/realtime/metrics.ts";
import * as $auth_login from "./routes/auth/login.tsx";
import * as $campaigns from "./routes/campaigns.tsx";
import * as $index from "./routes/index.tsx";
import * as $integrations from "./routes/integrations.tsx";
import * as $links from "./routes/links.tsx";
import * as $settings from "./routes/settings.tsx";
import * as $test_predictive_visualizations from "./routes/test-predictive-visualizations.tsx";
import * as $test_realtime_streaming from "./routes/test-realtime-streaming.tsx";
import * as $test_unified_dashboard from "./routes/test-unified-dashboard.tsx";
import * as $DashboardGrid from "./islands/DashboardGrid.tsx";
import * as $DashboardHeader from "./islands/DashboardHeader.tsx";
import * as $DashboardSidebar from "./islands/DashboardSidebar.tsx";
import * as $RealtimeMetricsStream from "./islands/RealtimeMetricsStream.tsx";
import * as $analytics_AttributionAnalysisPage from "./islands/analytics/AttributionAnalysisPage.tsx";
import * as $analytics_CohortAnalysisPage from "./islands/analytics/CohortAnalysisPage.tsx";
import * as $analytics_RealtimeAnalyticsPage from "./islands/analytics/RealtimeAnalyticsPage.tsx";
import * as $auth_LoginForm from "./islands/auth/LoginForm.tsx";
import * as $charts_D3AttributionComparison from "./islands/charts/D3AttributionComparison.tsx";
import * as $charts_D3BarChart from "./islands/charts/D3BarChart.tsx";
import * as $charts_D3BaseChart from "./islands/charts/D3BaseChart.tsx";
import * as $charts_D3CLVHistogram from "./islands/charts/D3CLVHistogram.tsx";
import * as $charts_D3ChurnGauge from "./islands/charts/D3ChurnGauge.tsx";
import * as $charts_D3ChurnGaugeRealtime from "./islands/charts/D3ChurnGaugeRealtime.tsx";
import * as $charts_D3CohortComparison from "./islands/charts/D3CohortComparison.tsx";
import * as $charts_D3CohortHeatmap from "./islands/charts/D3CohortHeatmap.tsx";
import * as $charts_D3FunnelChart from "./islands/charts/D3FunnelChart.tsx";
import * as $charts_D3LineChart from "./islands/charts/D3LineChart.tsx";
import * as $charts_D3RevenueForecasting from "./islands/charts/D3RevenueForecasting.tsx";
import * as $charts_D3SankeyFlow from "./islands/charts/D3SankeyFlow.tsx";
import * as $dashboard_EnhancedAnalyticsDashboard from "./islands/dashboard/EnhancedAnalyticsDashboard.tsx";
import * as $dashboard_KPIScorecard from "./islands/dashboard/KPIScorecard.tsx";
import * as $dashboard_RealtimeMetrics from "./islands/dashboard/RealtimeMetrics.tsx";
import * as $layout_MobileSidebar from "./islands/layout/MobileSidebar.tsx";
import * as $layout_NavigationController from "./islands/layout/NavigationController.tsx";
import * as $ui_DarkModeToggle from "./islands/ui/DarkModeToggle.tsx";
import { type Manifest } from "$fresh/server.ts";

const manifest = {
  routes: {
    "./routes/_404.tsx": $_404,
    "./routes/_app.tsx": $_app,
    "./routes/_layout.tsx": $_layout,
    "./routes/_middleware.ts": $_middleware,
    "./routes/analytics-dashboard.tsx": $analytics_dashboard,
    "./routes/analytics.tsx": $analytics,
    "./routes/analytics/attribution.tsx": $analytics_attribution,
    "./routes/analytics/cohorts.tsx": $analytics_cohorts,
    "./routes/analytics/d3-dashboard.tsx": $analytics_d3_dashboard,
    "./routes/analytics/enhanced.tsx": $analytics_enhanced,
    "./routes/analytics/realtime.tsx": $analytics_realtime,
    "./routes/api/analytics/[...path].ts": $api_analytics_path_,
    "./routes/api/analytics/enhanced/attribution/channels.ts":
      $api_analytics_enhanced_attribution_channels,
    "./routes/api/analytics/enhanced/attribution/journeys.ts":
      $api_analytics_enhanced_attribution_journeys,
    "./routes/api/analytics/enhanced/attribution/models.ts":
      $api_analytics_enhanced_attribution_models,
    "./routes/api/analytics/enhanced/cohorts/analysis.ts": $api_analytics_enhanced_cohorts_analysis,
    "./routes/api/analytics/enhanced/cohorts/retention-curves.ts":
      $api_analytics_enhanced_cohorts_retention_curves,
    "./routes/api/analytics/enhanced/dashboard.ts": $api_analytics_enhanced_dashboard,
    "./routes/api/analytics/enhanced/realtime.ts": $api_analytics_enhanced_realtime,
    "./routes/api/analytics/enhanced/realtime/events.ts": $api_analytics_enhanced_realtime_events,
    "./routes/api/analytics/enhanced/realtime/funnel.ts": $api_analytics_enhanced_realtime_funnel,
    "./routes/api/analytics/enhanced/realtime/geography.ts":
      $api_analytics_enhanced_realtime_geography,
    "./routes/api/analytics/enhanced/realtime/metrics.ts": $api_analytics_enhanced_realtime_metrics,
    "./routes/api/auth/login.ts": $api_auth_login,
    "./routes/api/auth/logout.ts": $api_auth_logout,
    "./routes/api/dashboard/metrics.ts": $api_dashboard_metrics,
    "./routes/api/dashboard/overview.ts": $api_dashboard_overview,
    "./routes/api/health.ts": $api_health,
    "./routes/api/realtime/metrics.ts": $api_realtime_metrics,
    "./routes/auth/login.tsx": $auth_login,
    "./routes/campaigns.tsx": $campaigns,
    "./routes/index.tsx": $index,
    "./routes/integrations.tsx": $integrations,
    "./routes/links.tsx": $links,
    "./routes/settings.tsx": $settings,
    "./routes/test-predictive-visualizations.tsx": $test_predictive_visualizations,
    "./routes/test-realtime-streaming.tsx": $test_realtime_streaming,
    "./routes/test-unified-dashboard.tsx": $test_unified_dashboard,
  },
  islands: {
    "./islands/DashboardGrid.tsx": $DashboardGrid,
    "./islands/DashboardHeader.tsx": $DashboardHeader,
    "./islands/DashboardSidebar.tsx": $DashboardSidebar,
    "./islands/RealtimeMetricsStream.tsx": $RealtimeMetricsStream,
    "./islands/analytics/AttributionAnalysisPage.tsx": $analytics_AttributionAnalysisPage,
    "./islands/analytics/CohortAnalysisPage.tsx": $analytics_CohortAnalysisPage,
    "./islands/analytics/RealtimeAnalyticsPage.tsx": $analytics_RealtimeAnalyticsPage,
    "./islands/auth/LoginForm.tsx": $auth_LoginForm,
    "./islands/charts/D3AttributionComparison.tsx": $charts_D3AttributionComparison,
    "./islands/charts/D3BarChart.tsx": $charts_D3BarChart,
    "./islands/charts/D3BaseChart.tsx": $charts_D3BaseChart,
    "./islands/charts/D3CLVHistogram.tsx": $charts_D3CLVHistogram,
    "./islands/charts/D3ChurnGauge.tsx": $charts_D3ChurnGauge,
    "./islands/charts/D3ChurnGaugeRealtime.tsx": $charts_D3ChurnGaugeRealtime,
    "./islands/charts/D3CohortComparison.tsx": $charts_D3CohortComparison,
    "./islands/charts/D3CohortHeatmap.tsx": $charts_D3CohortHeatmap,
    "./islands/charts/D3FunnelChart.tsx": $charts_D3FunnelChart,
    "./islands/charts/D3LineChart.tsx": $charts_D3LineChart,
    "./islands/charts/D3RevenueForecasting.tsx": $charts_D3RevenueForecasting,
    "./islands/charts/D3SankeyFlow.tsx": $charts_D3SankeyFlow,
    "./islands/dashboard/EnhancedAnalyticsDashboard.tsx": $dashboard_EnhancedAnalyticsDashboard,
    "./islands/dashboard/KPIScorecard.tsx": $dashboard_KPIScorecard,
    "./islands/dashboard/RealtimeMetrics.tsx": $dashboard_RealtimeMetrics,
    "./islands/layout/MobileSidebar.tsx": $layout_MobileSidebar,
    "./islands/layout/NavigationController.tsx": $layout_NavigationController,
    "./islands/ui/DarkModeToggle.tsx": $ui_DarkModeToggle,
  },
  baseUrl: import.meta.url,
} satisfies Manifest;

export default manifest;
